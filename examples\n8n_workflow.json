{"name": "Resume Generator Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "generate-resume", "responseMode": "onReceived", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "resume-generator"}, {"parameters": {"method": "POST", "url": "http://resume-api:8000/api/v1/generate-resume-base64", "jsonParameters": true, "options": {"timeout": 30000}, "bodyParametersJson": "={\n  \"data\": {{ $json.data }},\n  \"template\": \"{{ $json.template || 'modern' }}\",\n  \"format\": \"pdf\"\n}"}, "id": "generate-resume", "name": "Generate Resume", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"operation": "toBase64", "options": {}}, "id": "convert-base64", "name": "Process PDF", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"resource": "file", "operation": "upload", "fileId": {"__rl": true, "value": "={{ $json.pdf_base64 }}", "mode": "expression"}, "name": "={{ $json.filename || 'resume.pdf' }}", "options": {}}, "id": "save-to-drive", "name": "Save to Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"to": "={{ $('Webhook Trigger').first().json.email }}", "subject": "Your Resume is Ready!", "message": "=Hi {{ $('Webhook Trigger').first().json.data.personal_info.full_name }},\n\nYour resume has been generated successfully using the {{ $('Generate Resume').first().json.template }} template.\n\nYou can download it from: {{ $('Save to Google Drive').first().json.webViewLink }}\n\nBest regards,\nResume Generator", "options": {}}, "id": "send-email", "name": "Send Email Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Generate Resume", "type": "main", "index": 0}]]}, "Generate Resume": {"main": [[{"node": "Process PDF", "type": "main", "index": 0}]]}, "Process PDF": {"main": [[{"node": "Save to Google Drive", "type": "main", "index": 0}]]}, "Save to Google Drive": {"main": [[{"node": "Send Email Notification", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "America/New_York"}, "versionId": "1"}