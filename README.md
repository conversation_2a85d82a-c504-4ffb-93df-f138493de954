# Resume Builder API

A powerful, ATS-friendly resume generation API with multiple professional templates. Built with FastAPI and designed for easy integration with automation tools like n8n.

## Features

- 🎨 **4 Professional Templates**: Classic, Modern, Minimal, and Technical
- 📄 **ATS-Friendly**: Optimized for Applicant Tracking Systems
- 🚀 **Fast API**: Built with FastAPI for high performance
- 🐳 **Docker Ready**: Easy deployment with Docker and docker-compose
- 🔧 **n8n Integration**: Perfect for workflow automation
- 📊 **Auto Documentation**: Interactive API docs with Swagger UI
- ✅ **Data Validation**: Comprehensive input validation with Pydantic

## Quick Start

### Using Docker (Recommended)

1. **Clone and start the service:**
```bash
git clone <repository-url>
cd personal_resume_builder
docker-compose up -d
```

2. **Access the API:**
- API Base URL: `http://localhost:8000`
- Interactive Docs: `http://localhost:8000/api/v1/docs`
- Health Check: `http://localhost:8000/api/v1/health`

### Manual Installation

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Run the application:**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## API Endpoints

### Generate Resume
- **POST** `/api/v1/generate-resume`
- **POST** `/api/v1/generate-resume-base64` (for n8n integration)

### Get Templates
- **GET** `/api/v1/templates`

### Health Check
- **GET** `/api/v1/health`

## Templates

### 1. Classic
Traditional, professional layout perfect for corporate environments.
- Clean typography with Times New Roman
- Chronological format
- Conservative design

### 2. Modern
Contemporary design with subtle styling for creative industries.
- Calibri font with modern layout
- Two-column design
- Professional color accents

### 3. Minimal
Clean, minimalist design with lots of white space.
- Arial font
- Simple, distraction-free layout
- Focus on content

### 4. Technical
Optimized for technical roles with emphasis on skills and projects.
- Monospace headers with clean body text
- Skills and projects prominently featured
- Perfect for developers and engineers

## Usage Examples

### Basic Resume Generation

```bash
curl -X POST "http://localhost:8000/api/v1/generate-resume" \
  -H "Content-Type: application/json" \
  -d @examples/sample_request.json \
  --output resume.pdf
```

### Get Available Templates

```bash
curl -X GET "http://localhost:8000/api/v1/templates"
```

### Health Check

```bash
curl -X GET "http://localhost:8000/api/v1/health"
```

## n8n Integration

### Method 1: Direct PDF Download

1. **HTTP Request Node Configuration:**
   - Method: POST
   - URL: `http://localhost:8000/api/v1/generate-resume`
   - Body: JSON with resume data
   - Response: Binary (PDF file)

### Method 2: Base64 Response (Recommended for n8n)

1. **HTTP Request Node Configuration:**
   - Method: POST
   - URL: `http://localhost:8000/api/v1/generate-resume-base64`
   - Body: JSON with resume data
   - Response: JSON with base64 encoded PDF

2. **Process the Response:**
   - Extract `pdf_base64` from response
   - Decode base64 to binary for file operations
   - Use filename from response for saving

### Sample n8n Workflow

```json
{
  "nodes": [
    {
      "name": "Generate Resume",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "method": "POST",
        "url": "http://localhost:8000/api/v1/generate-resume-base64",
        "jsonParameters": true,
        "options": {},
        "bodyParametersJson": "={{ $json }}"
      }
    }
  ]
}
```

## Data Structure

### Resume Request Format

```json
{
  "data": {
    "personal_info": {
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "phone": "******-123-4567",
      "location": "New York, NY",
      "linkedin": "linkedin.com/in/johndoe",
      "github": "github.com/johndoe"
    },
    "summary": "Experienced software developer with 5+ years...",
    "experience": [...],
    "education": [...],
    "skills": [...],
    "projects": [...],
    "certifications": [...]
  },
  "template": "modern",
  "format": "pdf"
}
```

See `examples/sample_request.json` for a complete example.

## Development

### Project Structure
```
personal_resume_builder/
├── app/
│   ├── main.py          # FastAPI application
│   ├── models.py        # Pydantic data models
│   └── resume_generator.py # PDF generation logic
├── templates/           # Jinja2 HTML templates
├── static/css/         # CSS stylesheets
├── examples/           # Sample requests
├── Dockerfile          # Docker configuration
└── docker-compose.yml  # Docker Compose setup
```

### Adding New Templates

1. Create HTML template in `templates/`
2. Create corresponding CSS in `static/css/`
3. Add template info to the templates endpoint
4. Test with sample data

### Environment Variables

- `LOG_LEVEL`: Logging level (default: INFO)
- `PYTHONPATH`: Python path (set to /app in Docker)

## Deployment

### Production Considerations

1. **Security:**
   - Use HTTPS in production
   - Configure CORS appropriately
   - Add rate limiting
   - Use secrets for sensitive configuration

2. **Performance:**
   - Add Redis for caching
   - Use multiple workers
   - Configure load balancing

3. **Monitoring:**
   - Add application monitoring
   - Configure log aggregation
   - Set up health checks

### Docker Production Setup

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  resume-api:
    build: .
    environment:
      - LOG_LEVEL=WARNING
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the interactive API docs at `/api/v1/docs`
- Review the examples in the `examples/` directory
- Open an issue on GitHub
