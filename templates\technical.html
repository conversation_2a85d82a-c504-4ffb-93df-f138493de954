<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ data.personal_info.full_name }} - Resume</title>
    <!-- CSS will be injected by WeasyPrint -->
</head>
<body>
    <div class="resume">
        <!-- Technical Header -->
        <header class="header">
            <h1 class="name">{{ data.personal_info.full_name }}</h1>
            <div class="contact-info">
                <div class="contact-row">
                    <span>{{ data.personal_info.email }}</span>
                    <span>{{ data.personal_info.phone }}</span>
                    <span>{{ data.personal_info.location }}</span>
                </div>
                {% if data.personal_info.github or data.personal_info.linkedin %}
                <div class="contact-row">
                    {% if data.personal_info.github %}
                    <span>GitHub: {{ data.personal_info.github }}</span>
                    {% endif %}
                    {% if data.personal_info.linkedin %}
                    <span>LinkedIn: {{ data.personal_info.linkedin }}</span>
                    {% endif %}
                    {% if data.personal_info.website %}
                    <span>Portfolio: {{ data.personal_info.website }}</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </header>

        <!-- Technical Summary -->
        <section class="section">
            <h2 class="section-title">Technical Summary</h2>
            <p class="summary">{{ data.summary }}</p>
        </section>

        <!-- Skills Section (Prominent for Technical Roles) -->
        <section class="section skills-section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-container">
                {% set skills_per_row = (data.skills|length / 3)|round(0, 'ceil')|int %}
                {% for i in range(0, data.skills|length, skills_per_row) %}
                <div class="skills-column">
                    {% for skill in data.skills[i:i+skills_per_row] %}
                    <span class="skill-item">{{ skill }}</span>
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </section>

        <!-- Projects Section (Featured for Technical Roles) -->
        {% if data.projects %}
        <section class="section">
            <h2 class="section-title">Featured Projects</h2>
            {% for project in data.projects %}
            <div class="project-item">
                <div class="project-header">
                    <h3 class="project-name">{{ project.name }}</h3>
                    {% if project.date %}
                    <span class="project-date">{{ project.date }}</span>
                    {% endif %}
                </div>
                <p class="project-description">{{ project.description }}</p>
                <div class="project-details">
                    <div class="tech-stack">
                        <strong>Tech Stack:</strong>
                        {% for tech in project.technologies %}
                        <span class="tech-badge">{{ tech }}</span>
                        {% endfor %}
                    </div>
                    {% if project.url or project.github %}
                    <div class="project-links">
                        {% if project.github %}
                        <span class="link">Code: {{ project.github }}</span>
                        {% endif %}
                        {% if project.url %}
                        <span class="link">Demo: {{ project.url }}</span>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </section>
        {% endif %}

        <!-- Professional Experience -->
        <section class="section">
            <h2 class="section-title">Professional Experience</h2>
            {% for exp in data.experience %}
            <div class="experience-item">
                <div class="exp-header">
                    <div class="exp-title-company">
                        <h3 class="position">{{ exp.position }}</h3>
                        <span class="company">{{ exp.company }}</span>
                    </div>
                    <div class="exp-meta">
                        <span class="date-range">{{ exp.start_date }} - {{ exp.end_date }}</span>
                        {% if exp.location %}
                        <span class="location">{{ exp.location }}</span>
                        {% endif %}
                    </div>
                </div>
                <ul class="achievements">
                    {% for achievement in exp.description %}
                    <li>{{ achievement }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endfor %}
        </section>

        <!-- Education -->
        <section class="section">
            <h2 class="section-title">Education</h2>
            {% for edu in data.education %}
            <div class="education-item">
                <div class="edu-header">
                    <div class="edu-degree">
                        <h3 class="degree">{{ edu.degree }}</h3>
                        {% if edu.field_of_study %}
                        <span class="field">{{ edu.field_of_study }}</span>
                        {% endif %}
                    </div>
                    <span class="graduation-date">{{ edu.graduation_date }}</span>
                </div>
                <div class="edu-details">
                    <span class="institution">{{ edu.institution }}</span>
                    {% if edu.location %}
                    <span class="location">{{ edu.location }}</span>
                    {% endif %}
                    {% if edu.gpa %}
                    <span class="gpa">GPA: {{ edu.gpa }}</span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </section>

        <!-- Certifications (if provided) -->
        {% if data.certifications %}
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            <div class="certifications-grid">
                {% for cert in data.certifications %}
                <div class="certification-item">
                    <h3 class="cert-name">{{ cert.name }}</h3>
                    <p class="cert-issuer">{{ cert.issuer }}</p>
                    <p class="cert-date">{{ cert.date }}</p>
                    {% if cert.credential_id %}
                    <p class="credential-id">ID: {{ cert.credential_id }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>
        {% endif %}
    </div>
</body>
</html>
