{"data": {"personal_info": {"full_name": "<PERSON>", "email": "<EMAIL>", "phone": "******-123-4567", "location": "San Francisco, CA", "linkedin": "linkedin.com/in/sa<PERSON><PERSON><PERSON><PERSON>", "github": "github.com/sarah<PERSON><PERSON>son", "website": "sarahjohnson.dev"}, "summary": "Experienced Full-Stack Software Engineer with 6+ years of expertise in developing scalable web applications using modern technologies. Proven track record of leading cross-functional teams, implementing CI/CD pipelines, and delivering high-quality software solutions. Passionate about clean code, user experience, and continuous learning.", "experience": [{"company": "TechCorp Inc.", "position": "Senior Software Engineer", "start_date": "2022-01", "end_date": "Present", "location": "San Francisco, CA", "description": ["Led development of microservices architecture serving 1M+ daily active users, resulting in 40% improvement in system performance", "Mentored 3 junior developers and conducted code reviews, improving team code quality by 35%", "Implemented automated testing strategies that reduced production bugs by 50%", "Collaborated with product managers and designers to deliver 15+ feature releases on schedule"]}, {"company": "StartupXYZ", "position": "Full-<PERSON><PERSON>", "start_date": "2020-03", "end_date": "2021-12", "location": "Remote", "description": ["Built responsive web applications using React, Node.js, and PostgreSQL for 50K+ users", "Designed and implemented RESTful APIs with 99.9% uptime and sub-200ms response times", "Optimized database queries resulting in 60% faster page load times", "Integrated third-party payment systems and APIs including Stripe and SendGrid"]}, {"company": "Digital Solutions LLC", "position": "Software Developer", "start_date": "2018-06", "end_date": "2020-02", "location": "Austin, TX", "description": ["Developed and maintained e-commerce platforms using PHP, MySQL, and JavaScript", "Implemented responsive designs that increased mobile conversion rates by 25%", "Collaborated with QA team to establish testing protocols and reduce deployment issues", "Participated in agile development processes and sprint planning meetings"]}], "education": [{"institution": "University of California, Berkeley", "degree": "Bachelor of Science", "field_of_study": "Computer Science", "graduation_date": "2018-05", "gpa": "3.7", "location": "Berkeley, CA", "honors": ["Dean's List (Fall 2017, Spring 2018)", "Computer Science Department Award for Academic Excellence"]}], "skills": ["JavaScript", "TypeScript", "Python", "Java", "React", "Vue.js", "Node.js", "Express.js", "Django", "Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "<PERSON>er", "Kubernetes", "Git", "CI/CD", "Jest", "Cypress", "HTML5", "CSS3", "SASS", "Webpack", "REST APIs", "GraphQL", "Microservices"], "projects": [{"name": "E-Commerce Analytics Dashboard", "description": "Built a real-time analytics dashboard for e-commerce businesses using React, D3.js, and Node.js. Features include sales tracking, customer behavior analysis, and automated reporting with 95% accuracy in data visualization.", "technologies": ["React", "D3.js", "Node.js", "PostgreSQL", "Redis", "AWS"], "url": "https://analytics-dashboard-demo.com", "github": "https://github.com/sa<PERSON><PERSON><PERSON><PERSON>/ecommerce-analytics", "date": "2023-08"}, {"name": "Task Management API", "description": "Developed a RESTful API for task management with user authentication, real-time notifications, and team collaboration features. Supports 10K+ concurrent users with 99.9% uptime.", "technologies": ["Node.js", "Express.js", "MongoDB", "Socket.io", "JWT", "<PERSON>er"], "github": "https://github.com/sa<PERSON><PERSON><PERSON><PERSON>/task-api", "date": "2023-03"}, {"name": "Weather Forecast App", "description": "Created a responsive weather application with location-based forecasts, interactive maps, and push notifications. Integrated with multiple weather APIs for accurate predictions.", "technologies": ["Vue.js", "TypeScript", "PWA", "Chart.js", "Geolocation API"], "url": "https://weather-app-demo.netlify.app", "github": "https://github.com/sarah<PERSON><PERSON><PERSON>/weather-app", "date": "2022-11"}], "certifications": [{"name": "AWS Certified Solutions Architect - Associate", "issuer": "Amazon Web Services", "date": "2023-06", "expiry_date": "2026-06", "credential_id": "AWS-ASA-12345", "url": "https://aws.amazon.com/certification/"}, {"name": "Certified Kubernetes Administrator (CKA)", "issuer": "Cloud Native Computing Foundation", "date": "2023-02", "expiry_date": "2026-02", "credential_id": "CKA-67890"}, {"name": "Google Cloud Professional Developer", "issuer": "Google Cloud", "date": "2022-09", "expiry_date": "2024-09", "credential_id": "GCP-PD-54321"}]}, "template": "modern", "format": "pdf"}