<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ data.personal_info.full_name }} - Resume</title>
    <!-- CSS will be injected by WeasyPrint -->
</head>
<body>
    <div class="resume">
        <!-- Header Section with Modern Layout -->
        <header class="header">
            <div class="header-content">
                <h1 class="name">{{ data.personal_info.full_name }}</h1>
                <div class="contact-grid">
                    <div class="contact-item">
                        <span class="contact-label">Email</span>
                        <span class="contact-value">{{ data.personal_info.email }}</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">Phone</span>
                        <span class="contact-value">{{ data.personal_info.phone }}</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-label">Location</span>
                        <span class="contact-value">{{ data.personal_info.location }}</span>
                    </div>
                    {% if data.personal_info.linkedin %}
                    <div class="contact-item">
                        <span class="contact-label">LinkedIn</span>
                        <span class="contact-value">{{ data.personal_info.linkedin }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </header>

        <!-- Professional Summary with Modern Styling -->
        <section class="section summary-section">
            <h2 class="section-title">Professional Summary</h2>
            <div class="summary-content">
                <p class="summary">{{ data.summary }}</p>
            </div>
        </section>

        <!-- Two-Column Layout for Main Content -->
        <div class="main-content">
            <!-- Left Column -->
            <div class="left-column">
                <!-- Experience Section -->
                <section class="section">
                    <h2 class="section-title">Experience</h2>
                    {% for exp in data.experience %}
                    <div class="experience-item">
                        <div class="experience-timeline">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <h3 class="position">{{ exp.position }}</h3>
                                <div class="company-info">
                                    <span class="company">{{ exp.company }}</span>
                                    {% if exp.location %}
                                    <span class="separator">•</span>
                                    <span class="location">{{ exp.location }}</span>
                                    {% endif %}
                                </div>
                                <div class="date-range">{{ exp.start_date }} - {{ exp.end_date }}</div>
                                <ul class="achievements">
                                    {% for achievement in exp.description %}
                                    <li>{{ achievement }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </section>

                <!-- Projects Section (if provided) -->
                {% if data.projects %}
                <section class="section">
                    <h2 class="section-title">Featured Projects</h2>
                    {% for project in data.projects %}
                    <div class="project-item">
                        <h3 class="project-name">{{ project.name }}</h3>
                        <p class="project-description">{{ project.description }}</p>
                        <div class="project-tech">
                            {% for tech in project.technologies %}
                            <span class="tech-tag">{{ tech }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </section>
                {% endif %}
            </div>

            <!-- Right Column -->
            <div class="right-column">
                <!-- Skills Section -->
                <section class="section">
                    <h2 class="section-title">Skills</h2>
                    <div class="skills-grid">
                        {% for skill in data.skills %}
                        <div class="skill-item">{{ skill }}</div>
                        {% endfor %}
                    </div>
                </section>

                <!-- Education Section -->
                <section class="section">
                    <h2 class="section-title">Education</h2>
                    {% for edu in data.education %}
                    <div class="education-item">
                        <h3 class="degree">{{ edu.degree }}</h3>
                        {% if edu.field_of_study %}
                        <p class="field">{{ edu.field_of_study }}</p>
                        {% endif %}
                        <p class="institution">{{ edu.institution }}</p>
                        <p class="graduation-date">{{ edu.graduation_date }}</p>
                        {% if edu.gpa %}
                        <p class="gpa">GPA: {{ edu.gpa }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </section>

                <!-- Certifications Section (if provided) -->
                {% if data.certifications %}
                <section class="section">
                    <h2 class="section-title">Certifications</h2>
                    {% for cert in data.certifications %}
                    <div class="certification-item">
                        <h3 class="cert-name">{{ cert.name }}</h3>
                        <p class="cert-issuer">{{ cert.issuer }}</p>
                        <p class="cert-date">{{ cert.date }}</p>
                    </div>
                    {% endfor %}
                </section>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
