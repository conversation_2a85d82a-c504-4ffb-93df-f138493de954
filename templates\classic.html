<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ data.personal_info.full_name }} - Resume</title>
    <!-- CSS will be injected by WeasyPrint -->
</head>
<body>
    <div class="resume">
        <!-- Header Section -->
        <header class="header">
            <h1 class="name">{{ data.personal_info.full_name }}</h1>
            <div class="contact-info">
                <span class="contact-item">{{ data.personal_info.email }}</span>
                <span class="contact-item">{{ data.personal_info.phone }}</span>
                <span class="contact-item">{{ data.personal_info.location }}</span>
                {% if data.personal_info.linkedin %}
                <span class="contact-item">{{ data.personal_info.linkedin }}</span>
                {% endif %}
                {% if data.personal_info.github %}
                <span class="contact-item">{{ data.personal_info.github }}</span>
                {% endif %}
            </div>
        </header>

        <!-- Professional Summary -->
        <section class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p class="summary">{{ data.summary }}</p>
        </section>

        <!-- Experience Section -->
        <section class="section">
            <h2 class="section-title">Professional Experience</h2>
            {% for exp in data.experience %}
            <div class="experience-item">
                <div class="experience-header">
                    <h3 class="position">{{ exp.position }}</h3>
                    <span class="date-range">{{ exp.start_date }} - {{ exp.end_date }}</span>
                </div>
                <div class="company-location">
                    <span class="company">{{ exp.company }}</span>
                    {% if exp.location %}
                    <span class="location">{{ exp.location }}</span>
                    {% endif %}
                </div>
                <ul class="achievements">
                    {% for achievement in exp.description %}
                    <li>{{ achievement }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endfor %}
        </section>

        <!-- Education Section -->
        <section class="section">
            <h2 class="section-title">Education</h2>
            {% for edu in data.education %}
            <div class="education-item">
                <div class="education-header">
                    <h3 class="degree">{{ edu.degree }}{% if edu.field_of_study %} in {{ edu.field_of_study }}{% endif %}</h3>
                    <span class="date">{{ edu.graduation_date }}</span>
                </div>
                <div class="institution-info">
                    <span class="institution">{{ edu.institution }}</span>
                    {% if edu.location %}
                    <span class="location">{{ edu.location }}</span>
                    {% endif %}
                </div>
                {% if edu.gpa %}
                <p class="gpa">GPA: {{ edu.gpa }}</p>
                {% endif %}
                {% if edu.honors %}
                <ul class="honors">
                    {% for honor in edu.honors %}
                    <li>{{ honor }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </section>

        <!-- Skills Section -->
        <section class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-list">
                {% for skill in data.skills %}
                <span class="skill">{{ skill }}</span>{% if not loop.last %}, {% endif %}
                {% endfor %}
            </div>
        </section>

        <!-- Projects Section (if provided) -->
        {% if data.projects %}
        <section class="section">
            <h2 class="section-title">Projects</h2>
            {% for project in data.projects %}
            <div class="project-item">
                <div class="project-header">
                    <h3 class="project-name">{{ project.name }}</h3>
                    {% if project.date %}
                    <span class="project-date">{{ project.date }}</span>
                    {% endif %}
                </div>
                <p class="project-description">{{ project.description }}</p>
                <div class="project-tech">
                    <strong>Technologies:</strong>
                    {% for tech in project.technologies %}
                    <span class="tech">{{ tech }}</span>{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </div>
                {% if project.url or project.github %}
                <div class="project-links">
                    {% if project.url %}
                    <span class="project-link">Live: {{ project.url }}</span>
                    {% endif %}
                    {% if project.github %}
                    <span class="project-link">GitHub: {{ project.github }}</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </section>
        {% endif %}

        <!-- Certifications Section (if provided) -->
        {% if data.certifications %}
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            {% for cert in data.certifications %}
            <div class="certification-item">
                <div class="cert-header">
                    <h3 class="cert-name">{{ cert.name }}</h3>
                    <span class="cert-date">{{ cert.date }}</span>
                </div>
                <p class="cert-issuer">{{ cert.issuer }}</p>
                {% if cert.credential_id %}
                <p class="credential-id">Credential ID: {{ cert.credential_id }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </section>
        {% endif %}
    </div>
</body>
</html>
