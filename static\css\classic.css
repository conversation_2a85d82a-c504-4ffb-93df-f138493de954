/* Classic Resume Template - ATS Friendly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Times New Roman', Times, serif;
    font-size: 11pt;
    line-height: 1.4;
    color: #000000;
    background: white;
}

.resume {
    max-width: 8.5in;
    margin: 0 auto;
    padding: 0.75in;
    background: white;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 20pt;
    border-bottom: 1pt solid #000000;
    padding-bottom: 10pt;
}

.name {
    font-size: 18pt;
    font-weight: bold;
    margin-bottom: 8pt;
    text-transform: uppercase;
    letter-spacing: 1pt;
}

.contact-info {
    font-size: 10pt;
    line-height: 1.3;
}

.contact-item {
    margin: 0 8pt;
}

.contact-item:first-child {
    margin-left: 0;
}

/* Section Styles */
.section {
    margin-bottom: 16pt;
}

.section-title {
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase;
    border-bottom: 0.5pt solid #000000;
    padding-bottom: 2pt;
    margin-bottom: 8pt;
    letter-spacing: 0.5pt;
}

/* Summary */
.summary {
    text-align: justify;
    margin-bottom: 12pt;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 12pt;
    page-break-inside: avoid;
}

.experience-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 2pt;
}

.position {
    font-size: 11pt;
    font-weight: bold;
}

.date-range {
    font-size: 10pt;
    font-style: italic;
}

.company-location {
    margin-bottom: 4pt;
    font-size: 10pt;
}

.company {
    font-weight: bold;
}

.location {
    margin-left: 8pt;
    font-style: italic;
}

.achievements {
    margin-left: 16pt;
    margin-bottom: 8pt;
}

.achievements li {
    margin-bottom: 2pt;
    text-align: justify;
}

/* Education Styles */
.education-item {
    margin-bottom: 10pt;
    page-break-inside: avoid;
}

.education-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 2pt;
}

.degree {
    font-size: 11pt;
    font-weight: bold;
}

.date {
    font-size: 10pt;
    font-style: italic;
}

.institution-info {
    margin-bottom: 2pt;
    font-size: 10pt;
}

.institution {
    font-weight: bold;
}

.gpa {
    font-size: 10pt;
    margin-bottom: 2pt;
}

.honors {
    margin-left: 16pt;
    font-size: 10pt;
}

.honors li {
    margin-bottom: 1pt;
}

/* Skills Styles */
.skills-list {
    text-align: justify;
    line-height: 1.5;
}

.skill {
    font-weight: normal;
}

/* Projects Styles */
.project-item {
    margin-bottom: 10pt;
    page-break-inside: avoid;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 2pt;
}

.project-name {
    font-size: 11pt;
    font-weight: bold;
}

.project-date {
    font-size: 10pt;
    font-style: italic;
}

.project-description {
    margin-bottom: 4pt;
    text-align: justify;
}

.project-tech {
    font-size: 10pt;
    margin-bottom: 2pt;
}

.tech {
    font-style: italic;
}

.project-links {
    font-size: 10pt;
}

.project-link {
    margin-right: 12pt;
}

/* Certifications Styles */
.certification-item {
    margin-bottom: 8pt;
    page-break-inside: avoid;
}

.cert-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 2pt;
}

.cert-name {
    font-size: 11pt;
    font-weight: bold;
}

.cert-date {
    font-size: 10pt;
    font-style: italic;
}

.cert-issuer {
    font-size: 10pt;
    margin-bottom: 2pt;
}

.credential-id {
    font-size: 9pt;
    color: #333333;
}

/* Print Styles */
@media print {
    .resume {
        margin: 0;
        padding: 0.5in;
    }
    
    .section {
        page-break-inside: avoid;
    }
    
    .experience-item,
    .education-item,
    .project-item,
    .certification-item {
        page-break-inside: avoid;
    }
}
