import os
import logging
from pathlib import Path
from typing import Optional
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import tempfile

from .models import ResumeData

logger = logging.getLogger(__name__)


class ResumeGenerator:
    """Resume generator using Jinja2 templates and WeasyPrint for PDF generation"""
    
    def __init__(self):
        # Get the project root directory
        self.project_root = Path(__file__).parent.parent
        self.templates_dir = self.project_root / "templates"
        self.static_dir = self.project_root / "static"
        
        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=True
        )
        
        # CSS directory
        self.css_dir = self.static_dir / "css"
        
        logger.info(f"ResumeGenerator initialized with templates dir: {self.templates_dir}")
        logger.info(f"Static dir: {self.static_dir}")
    
    def _get_template_path(self, template_name: str) -> Path:
        """Get the full path to a template file"""
        template_path = self.templates_dir / f"{template_name}.html"
        if not template_path.exists():
            raise FileNotFoundError(f"Template '{template_name}' not found at {template_path}")
        return template_path
    
    def _get_css_path(self, template_name: str) -> Path:
        """Get the full path to a CSS file"""
        css_path = self.static_dir / "css" / f"{template_name}.css"
        if not css_path.exists():
            raise FileNotFoundError(f"CSS file for template '{template_name}' not found at {css_path}")
        return css_path
    
    def _render_html(self, template_name: str, resume_data: ResumeData) -> str:
        """Render HTML template with resume data"""
        try:
            template = self.jinja_env.get_template(f"{template_name}.html")
            html_content = template.render(data=resume_data)
            logger.info(f"Successfully rendered HTML for template: {template_name}")
            return html_content
        except TemplateNotFound:
            raise FileNotFoundError(f"Template '{template_name}.html' not found")
        except Exception as e:
            logger.error(f"Error rendering template {template_name}: {str(e)}")
            raise
    
    def _create_pdf_from_html(self, html_content: str, css_path: Path) -> bytes:
        """Convert HTML content to PDF using WeasyPrint"""
        try:
            # Create HTML object from string
            html_doc = HTML(string=html_content, base_url=str(self.project_root))

            # Load CSS
            css_doc = CSS(filename=str(css_path))

            # Generate PDF
            pdf_bytes = html_doc.write_pdf(stylesheets=[css_doc])

            logger.info(f"Successfully generated PDF, size: {len(pdf_bytes)} bytes")
            return pdf_bytes

        except Exception as e:
            logger.error(f"Error generating PDF: {str(e)}")
            raise
    
    async def generate_pdf(self, resume_data: ResumeData, template_name: str) -> bytes:
        """
        Generate a PDF resume from resume data and template name
        
        Args:
            resume_data: The resume data to render
            template_name: Name of the template to use (classic, modern, minimal, technical)
            
        Returns:
            bytes: PDF content as bytes
            
        Raises:
            FileNotFoundError: If template or CSS file not found
            Exception: If PDF generation fails
        """
        try:
            logger.info(f"Starting PDF generation for template: {template_name}")
            
            # Validate template exists
            template_path = self._get_template_path(template_name)
            css_path = self._get_css_path(template_name)
            
            # Render HTML
            html_content = self._render_html(template_name, resume_data)
            
            # Generate PDF
            pdf_bytes = self._create_pdf_from_html(html_content, css_path)
            
            logger.info(f"PDF generation completed successfully for template: {template_name}")
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"PDF generation failed for template {template_name}: {str(e)}")
            raise
    
    def get_available_templates(self) -> list[str]:
        """Get list of available template names"""
        templates = []
        for template_file in self.templates_dir.glob("*.html"):
            template_name = template_file.stem
            css_file = self.static_dir / "css" / f"{template_name}.css"
            if css_file.exists():
                templates.append(template_name)
        return sorted(templates)
    
    def validate_template(self, template_name: str) -> bool:
        """Validate that a template and its CSS file exist"""
        try:
            self._get_template_path(template_name)
            self._get_css_path(template_name)
            return True
        except FileNotFoundError:
            return False
