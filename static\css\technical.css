/* Technical Resume Template - ATS Friendly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 10pt;
    line-height: 1.5;
    color: #2c3e50;
    background: white;
}

.resume {
    max-width: 8.5in;
    margin: 0 auto;
    padding: 0.75in;
    background: white;
}

/* Header Styles */
.header {
    border: 1pt solid #34495e;
    padding: 15pt;
    margin-bottom: 20pt;
    background: #f8f9fa;
}

.name {
    font-size: 16pt;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10pt;
    text-align: center;
    font-family: 'Arial', sans-serif;
}

.contact-info {
    text-align: center;
}

.contact-row {
    margin-bottom: 4pt;
    font-size: 9pt;
}

.contact-row span {
    margin: 0 12pt;
}

.contact-row span:first-child {
    margin-left: 0;
}

/* Section Styles */
.section {
    margin-bottom: 18pt;
}

.section-title {
    font-size: 12pt;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 10pt;
    text-transform: uppercase;
    letter-spacing: 1pt;
    border-bottom: 1pt solid #bdc3c7;
    padding-bottom: 3pt;
    font-family: 'Arial', sans-serif;
}

/* Summary */
.summary {
    font-size: 10pt;
    text-align: justify;
    margin-bottom: 12pt;
    padding: 10pt;
    background: #ecf0f1;
    border-left: 3pt solid #3498db;
    font-family: 'Arial', sans-serif;
}

/* Skills Section - Prominent for Technical */
.skills-section {
    background: #f8f9fa;
    padding: 15pt;
    margin: 0 -15pt 20pt -15pt;
    border: 1pt solid #bdc3c7;
}

.skills-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15pt;
}

.skills-column {
    display: flex;
    flex-direction: column;
    gap: 4pt;
}

.skill-item {
    background: #3498db;
    color: white;
    padding: 3pt 8pt;
    border-radius: 2pt;
    font-size: 9pt;
    text-align: center;
    font-weight: bold;
    font-family: 'Arial', sans-serif;
}

/* Projects Section - Featured */
.project-item {
    margin-bottom: 16pt;
    border: 1pt solid #ecf0f1;
    padding: 12pt;
    page-break-inside: avoid;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 6pt;
    border-bottom: 1pt dotted #bdc3c7;
    padding-bottom: 4pt;
}

.project-name {
    font-size: 11pt;
    font-weight: bold;
    color: #2c3e50;
    font-family: 'Arial', sans-serif;
}

.project-date {
    font-size: 9pt;
    color: #7f8c8d;
}

.project-description {
    margin-bottom: 8pt;
    text-align: justify;
    font-family: 'Arial', sans-serif;
}

.project-details {
    background: #f8f9fa;
    padding: 8pt;
    margin-top: 8pt;
}

.tech-stack {
    margin-bottom: 6pt;
}

.tech-badge {
    background: #34495e;
    color: white;
    padding: 2pt 6pt;
    margin: 2pt;
    border-radius: 2pt;
    font-size: 8pt;
    display: inline-block;
    font-family: 'Arial', sans-serif;
}

.project-links {
    font-size: 9pt;
}

.link {
    margin-right: 15pt;
    color: #3498db;
    font-weight: bold;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 14pt;
    page-break-inside: avoid;
}

.exp-header {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 15pt;
    margin-bottom: 6pt;
    padding-bottom: 4pt;
    border-bottom: 1pt dotted #bdc3c7;
}

.exp-title-company {
    display: flex;
    flex-direction: column;
}

.position {
    font-size: 11pt;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 2pt;
    font-family: 'Arial', sans-serif;
}

.company {
    font-size: 10pt;
    color: #3498db;
    font-weight: bold;
}

.exp-meta {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.date-range {
    font-size: 9pt;
    color: #7f8c8d;
    font-weight: bold;
}

.location {
    font-size: 9pt;
    color: #7f8c8d;
    margin-top: 2pt;
}

.achievements {
    margin-left: 16pt;
    font-family: 'Arial', sans-serif;
}

.achievements li {
    margin-bottom: 3pt;
    text-align: justify;
}

/* Education Styles */
.education-item {
    margin-bottom: 12pt;
    page-break-inside: avoid;
}

.edu-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 4pt;
}

.edu-degree {
    display: flex;
    flex-direction: column;
}

.degree {
    font-size: 11pt;
    font-weight: bold;
    color: #2c3e50;
    font-family: 'Arial', sans-serif;
}

.field {
    font-size: 10pt;
    color: #7f8c8d;
    margin-top: 2pt;
}

.graduation-date {
    font-size: 9pt;
    color: #7f8c8d;
    font-weight: bold;
}

.edu-details {
    font-size: 10pt;
    color: #7f8c8d;
}

.edu-details span {
    margin-right: 12pt;
}

/* Certifications Grid */
.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200pt, 1fr));
    gap: 12pt;
}

.certification-item {
    border: 1pt solid #ecf0f1;
    padding: 10pt;
    background: #f8f9fa;
    page-break-inside: avoid;
}

.cert-name {
    font-size: 10pt;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 4pt;
    font-family: 'Arial', sans-serif;
}

.cert-issuer {
    font-size: 9pt;
    color: #3498db;
    margin-bottom: 2pt;
}

.cert-date {
    font-size: 9pt;
    color: #7f8c8d;
}

.credential-id {
    font-size: 8pt;
    color: #7f8c8d;
    margin-top: 2pt;
}

/* Print Styles */
@media print {
    .resume {
        margin: 0;
        padding: 0.5in;
    }
    
    .skills-section {
        margin: 0 -10pt 15pt -10pt;
        padding: 10pt;
    }
    
    .skills-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .certifications-grid {
        grid-template-columns: 1fr;
    }
    
    .section {
        page-break-inside: avoid;
    }
}
