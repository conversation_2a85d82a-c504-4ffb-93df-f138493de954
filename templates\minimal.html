<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ data.personal_info.full_name }} - Resume</title>
    <!-- CSS will be injected by WeasyPrint -->
</head>
<body>
    <div class="resume">
        <!-- <PERSON><PERSON>er -->
        <header class="header">
            <h1 class="name">{{ data.personal_info.full_name }}</h1>
            <div class="contact-line">
                {{ data.personal_info.email }} • {{ data.personal_info.phone }} • {{ data.personal_info.location }}
                {% if data.personal_info.linkedin %} • {{ data.personal_info.linkedin }}{% endif %}
            </div>
        </header>

        <!-- Summary -->
        <section class="section">
            <p class="summary">{{ data.summary }}</p>
        </section>

        <!-- Experience -->
        <section class="section">
            <h2 class="section-title">Experience</h2>
            {% for exp in data.experience %}
            <div class="item">
                <div class="item-header">
                    <span class="title">{{ exp.position }}, {{ exp.company }}</span>
                    <span class="date">{{ exp.start_date }} – {{ exp.end_date }}</span>
                </div>
                {% if exp.location %}
                <div class="location">{{ exp.location }}</div>
                {% endif %}
                <ul class="description">
                    {% for achievement in exp.description %}
                    <li>{{ achievement }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endfor %}
        </section>

        <!-- Education -->
        <section class="section">
            <h2 class="section-title">Education</h2>
            {% for edu in data.education %}
            <div class="item">
                <div class="item-header">
                    <span class="title">{{ edu.degree }}{% if edu.field_of_study %}, {{ edu.field_of_study }}{% endif %}</span>
                    <span class="date">{{ edu.graduation_date }}</span>
                </div>
                <div class="institution">{{ edu.institution }}{% if edu.location %}, {{ edu.location }}{% endif %}</div>
                {% if edu.gpa %}
                <div class="gpa">GPA: {{ edu.gpa }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </section>

        <!-- Skills -->
        <section class="section">
            <h2 class="section-title">Skills</h2>
            <div class="skills">
                {% for skill in data.skills %}{{ skill }}{% if not loop.last %}, {% endif %}{% endfor %}
            </div>
        </section>

        <!-- Projects (if provided) -->
        {% if data.projects %}
        <section class="section">
            <h2 class="section-title">Projects</h2>
            {% for project in data.projects %}
            <div class="item">
                <div class="item-header">
                    <span class="title">{{ project.name }}</span>
                    {% if project.date %}
                    <span class="date">{{ project.date }}</span>
                    {% endif %}
                </div>
                <div class="description">{{ project.description }}</div>
                <div class="technologies">
                    Technologies: {% for tech in project.technologies %}{{ tech }}{% if not loop.last %}, {% endif %}{% endfor %}
                </div>
            </div>
            {% endfor %}
        </section>
        {% endif %}

        <!-- Certifications (if provided) -->
        {% if data.certifications %}
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            {% for cert in data.certifications %}
            <div class="item">
                <div class="item-header">
                    <span class="title">{{ cert.name }}</span>
                    <span class="date">{{ cert.date }}</span>
                </div>
                <div class="issuer">{{ cert.issuer }}</div>
            </div>
            {% endfor %}
        </section>
        {% endif %}
    </div>
</body>
</html>
