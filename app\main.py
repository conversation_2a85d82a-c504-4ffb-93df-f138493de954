from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
import logging
from typing import List
import os

from .models import (
    ResumeRequest, 
    TemplateInfo, 
    HealthResponse, 
    TemplateType
)
from .resume_generator import ResumeGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Resume Builder API",
    description="Generate ATS-friendly resumes with multiple templates",
    version="1.0.0",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize resume generator
resume_generator = ResumeGenerator()

@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        templates_count = len(list(TemplateType))
        return HealthResponse(
            status="healthy",
            version="1.0.0",
            templates_available=templates_count
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Service unhealthy")


@app.get("/api/v1/templates", response_model=List[TemplateInfo])
async def get_templates():
    """Get list of available resume templates"""
    templates = [
        TemplateInfo(
            name="classic",
            description="Traditional, professional layout with clean typography. Perfect for corporate environments and traditional industries."
        ),
        TemplateInfo(
            name="modern",
            description="Contemporary design with subtle styling. Great for creative and tech industries while maintaining ATS compatibility."
        ),
        TemplateInfo(
            name="minimal",
            description="Clean, minimalist design with lots of white space. Ideal for highlighting content without distractions."
        ),
        TemplateInfo(
            name="technical",
            description="Optimized for technical roles with emphasis on skills and projects. Perfect for software developers and engineers."
        )
    ]
    return templates


@app.post("/api/v1/generate-resume")
async def generate_resume(request: ResumeRequest):
    """Generate a resume PDF from provided data"""
    try:
        logger.info(f"Generating resume with template: {request.template}")
        
        # Generate the PDF
        pdf_content = await resume_generator.generate_pdf(
            resume_data=request.data,
            template_name=request.template.value
        )
        
        # Return PDF as response
        return Response(
            content=pdf_content,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=resume_{request.template.value}.pdf"
            }
        )
        
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except FileNotFoundError as e:
        logger.error(f"Template not found: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Template '{request.template}' not found")
    except Exception as e:
        logger.error(f"Resume generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate resume")


@app.post("/api/v1/generate-resume-base64")
async def generate_resume_base64(request: ResumeRequest):
    """Generate a resume PDF and return as base64 encoded string (useful for n8n)"""
    try:
        import base64
        
        logger.info(f"Generating resume with template: {request.template}")
        
        # Generate the PDF
        pdf_content = await resume_generator.generate_pdf(
            resume_data=request.data,
            template_name=request.template.value
        )
        
        # Encode as base64
        pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
        
        return JSONResponse(content={
            "success": True,
            "template": request.template.value,
            "filename": f"resume_{request.template.value}.pdf",
            "pdf_base64": pdf_base64,
            "size_bytes": len(pdf_content)
        })
        
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except FileNotFoundError as e:
        logger.error(f"Template not found: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Template '{request.template}' not found")
    except Exception as e:
        logger.error(f"Resume generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to generate resume")


# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Resume Builder API",
        "version": "1.0.0",
        "docs": "/api/v1/docs"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
