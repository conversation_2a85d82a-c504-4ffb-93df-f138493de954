/* Modern Resume Template - ATS Friendly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>ri', 'Arial', sans-serif;
    font-size: 11pt;
    line-height: 1.5;
    color: #2c3e50;
    background: white;
}

.resume {
    max-width: 8.5in;
    margin: 0 auto;
    padding: 0.75in;
    background: white;
}

/* Header Styles */
.header {
    background: #f8f9fa;
    padding: 20pt;
    margin: -0.75in -0.75in 20pt -0.75in;
    border-bottom: 2pt solid #3498db;
}

.header-content {
    max-width: 7in;
    margin: 0 auto;
}

.name {
    font-size: 24pt;
    font-weight: 300;
    color: #2c3e50;
    margin-bottom: 12pt;
    text-align: center;
}

.contact-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}

.contact-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10pt;
}

.contact-label {
    font-size: 9pt;
    font-weight: bold;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5pt;
}

.contact-value {
    font-size: 10pt;
    color: #2c3e50;
    margin-top: 2pt;
}

/* Section Styles */
.section {
    margin-bottom: 18pt;
}

.section-title {
    font-size: 14pt;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 10pt;
    text-transform: uppercase;
    letter-spacing: 1pt;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -3pt;
    left: 0;
    width: 30pt;
    height: 1pt;
    background: #3498db;
}

/* Summary Section */
.summary-section {
    background: #f8f9fa;
    padding: 15pt;
    margin: 0 -15pt 20pt -15pt;
    border-left: 3pt solid #3498db;
}

.summary-content {
    max-width: 6in;
}

.summary {
    font-size: 11pt;
    text-align: justify;
    color: #2c3e50;
    font-style: italic;
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex-direction: row;
}

.left-column {
    flex: 2;
    margin-right: 30pt;
}

.right-column {
    flex: 1;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 16pt;
    page-break-inside: avoid;
}

.experience-timeline {
    display: flex;
    align-items: flex-start;
}

.timeline-dot {
    width: 8pt;
    height: 8pt;
    background: #3498db;
    border-radius: 50%;
    margin-top: 6pt;
    margin-right: 12pt;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
    min-width: 0;
}

.position {
    font-size: 12pt;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2pt;
}

.company-info {
    margin-bottom: 4pt;
    font-size: 10pt;
}

.company {
    font-weight: 500;
    color: #3498db;
}

.separator {
    margin: 0 6pt;
    color: #bdc3c7;
}

.location {
    color: #7f8c8d;
}

.date-range {
    font-size: 9pt;
    color: #7f8c8d;
    font-weight: 500;
    margin-bottom: 6pt;
}

.achievements {
    margin-left: 12pt;
}

.achievements li {
    margin-bottom: 3pt;
    text-align: justify;
}

/* Skills Styles */
.skills-grid {
    display: flex;
    flex-wrap: wrap;
}

.skill-item {
    background: #ecf0f1;
    padding: 4pt 8pt;
    border-radius: 3pt;
    font-size: 9pt;
    text-align: center;
    color: #2c3e50;
    font-weight: 500;
    margin: 2pt;
}

/* Education Styles */
.education-item {
    margin-bottom: 12pt;
    page-break-inside: avoid;
}

.degree {
    font-size: 11pt;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2pt;
}

.field {
    font-size: 10pt;
    color: #7f8c8d;
    margin-bottom: 2pt;
}

.institution {
    font-size: 10pt;
    font-weight: 500;
    color: #3498db;
    margin-bottom: 2pt;
}

.graduation-date {
    font-size: 9pt;
    color: #7f8c8d;
}

.gpa {
    font-size: 9pt;
    color: #7f8c8d;
}

/* Projects Styles */
.project-item {
    margin-bottom: 14pt;
    page-break-inside: avoid;
}

.project-name {
    font-size: 11pt;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4pt;
}

.project-description {
    font-size: 10pt;
    text-align: justify;
    margin-bottom: 6pt;
    color: #2c3e50;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
}

.tech-tag {
    background: #3498db;
    color: white;
    padding: 2pt 6pt;
    border-radius: 2pt;
    font-size: 8pt;
    font-weight: 500;
    margin: 2pt;
}

/* Certifications Styles */
.certification-item {
    margin-bottom: 10pt;
    page-break-inside: avoid;
}

.cert-name {
    font-size: 10pt;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2pt;
}

.cert-issuer {
    font-size: 9pt;
    color: #3498db;
    margin-bottom: 1pt;
}

.cert-date {
    font-size: 9pt;
    color: #7f8c8d;
}

/* Print Styles */
@media print {
    .resume {
        margin: 0;
        padding: 0.5in;
    }
    
    .header {
        margin: -0.5in -0.5in 15pt -0.5in;
        padding: 15pt;
    }
    
    .main-content {
        flex-direction: column;
    }

    .left-column {
        margin-right: 0;
        margin-bottom: 15pt;
    }
    
    .section {
        page-break-inside: avoid;
    }
}
