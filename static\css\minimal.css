/* Minimal Resume Template - ATS Friendly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Helvetica', sans-serif;
    font-size: 11pt;
    line-height: 1.6;
    color: #000000;
    background: white;
}

.resume {
    max-width: 8.5in;
    margin: 0 auto;
    padding: 1in;
    background: white;
}

/* Header Styles */
.header {
    margin-bottom: 30pt;
}

.name {
    font-size: 20pt;
    font-weight: 300;
    margin-bottom: 8pt;
    color: #000000;
}

.contact-line {
    font-size: 10pt;
    color: #666666;
    line-height: 1.4;
}

/* Section Styles */
.section {
    margin-bottom: 24pt;
}

.section-title {
    font-size: 12pt;
    font-weight: 600;
    margin-bottom: 12pt;
    color: #000000;
    text-transform: uppercase;
    letter-spacing: 1pt;
}

/* Summary */
.summary {
    font-size: 11pt;
    text-align: justify;
    margin-bottom: 16pt;
    color: #333333;
}

/* Item Styles (Used for Experience, Education, etc.) */
.item {
    margin-bottom: 16pt;
    page-break-inside: avoid;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 4pt;
}

.title {
    font-size: 11pt;
    font-weight: 600;
    color: #000000;
}

.date {
    font-size: 10pt;
    color: #666666;
    font-weight: 400;
}

.location {
    font-size: 10pt;
    color: #666666;
    margin-bottom: 4pt;
}

.institution {
    font-size: 10pt;
    color: #666666;
    margin-bottom: 2pt;
}

.gpa {
    font-size: 10pt;
    color: #666666;
}

/* Description Lists */
.description {
    margin-left: 16pt;
    margin-bottom: 8pt;
}

.description li {
    margin-bottom: 3pt;
    text-align: justify;
}

/* Skills */
.skills {
    text-align: justify;
    line-height: 1.8;
    font-size: 11pt;
}

/* Project Specific Styles */
.technologies {
    font-size: 10pt;
    color: #666666;
    margin-top: 4pt;
    font-style: italic;
}

/* Certification Specific Styles */
.issuer {
    font-size: 10pt;
    color: #666666;
    margin-bottom: 2pt;
}

/* Clean spacing and typography */
h1, h2, h3 {
    font-weight: 600;
}

p {
    margin-bottom: 8pt;
}

ul {
    list-style-type: disc;
}

li {
    margin-bottom: 2pt;
}

/* Ensure clean breaks */
.item:last-child {
    margin-bottom: 0;
}

.section:last-child {
    margin-bottom: 0;
}

/* Print Styles */
@media print {
    .resume {
        margin: 0;
        padding: 0.75in;
    }
    
    .section {
        page-break-inside: avoid;
    }
    
    .item {
        page-break-inside: avoid;
    }
    
    /* Ensure good page breaks */
    .section-title {
        page-break-after: avoid;
    }
    
    .item-header {
        page-break-after: avoid;
    }
}
