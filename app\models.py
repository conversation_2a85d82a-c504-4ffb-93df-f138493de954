from pydantic import BaseModel, EmailStr, Field, validator
from typing import List, Optional
from datetime import date
from enum import Enum


class TemplateType(str, Enum):
    classic = "classic"
    modern = "modern"
    minimal = "minimal"
    technical = "technical"


class PersonalInfo(BaseModel):
    full_name: str = Field(..., min_length=2, max_length=100)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=20)
    location: str = Field(..., min_length=2, max_length=100)
    linkedin: Optional[str] = None
    github: Optional[str] = None
    website: Optional[str] = None
    
    @validator('phone')
    def validate_phone(cls, v):
        # Remove common phone formatting characters
        cleaned = ''.join(filter(str.isdigit, v))
        if len(cleaned) < 10:
            raise ValueError('Phone number must contain at least 10 digits')
        return v


class Experience(BaseModel):
    company: str = Field(..., min_length=1, max_length=100)
    position: str = Field(..., min_length=1, max_length=100)
    start_date: str = Field(..., description="Format: YYYY-MM or 'Present'")
    end_date: str = Field(..., description="Format: YYYY-MM or 'Present'")
    location: Optional[str] = Field(None, max_length=100)
    description: List[str] = Field(..., min_items=1, max_items=10)
    
    @validator('description')
    def validate_description(cls, v):
        for item in v:
            if len(item.strip()) < 10:
                raise ValueError('Each description item must be at least 10 characters')
        return v


class Education(BaseModel):
    institution: str = Field(..., min_length=1, max_length=100)
    degree: str = Field(..., min_length=1, max_length=100)
    field_of_study: Optional[str] = Field(None, max_length=100)
    graduation_date: str = Field(..., description="Format: YYYY-MM or YYYY")
    gpa: Optional[str] = Field(None, max_length=10)
    location: Optional[str] = Field(None, max_length=100)
    honors: Optional[List[str]] = None


class Project(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=10, max_length=500)
    technologies: List[str] = Field(..., min_items=1, max_items=15)
    url: Optional[str] = None
    github: Optional[str] = None
    date: Optional[str] = Field(None, description="Format: YYYY-MM")


class Certification(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    issuer: str = Field(..., min_length=1, max_length=100)
    date: str = Field(..., description="Format: YYYY-MM")
    expiry_date: Optional[str] = Field(None, description="Format: YYYY-MM")
    credential_id: Optional[str] = None
    url: Optional[str] = None


class ResumeData(BaseModel):
    personal_info: PersonalInfo
    summary: str = Field(..., min_length=50, max_length=500)
    experience: List[Experience] = Field(..., min_items=1, max_items=10)
    education: List[Education] = Field(..., min_items=1, max_items=5)
    skills: List[str] = Field(..., min_items=3, max_items=30)
    projects: Optional[List[Project]] = Field(None, max_items=5)
    certifications: Optional[List[Certification]] = Field(None, max_items=10)


class ResumeRequest(BaseModel):
    data: ResumeData
    template: TemplateType = TemplateType.classic
    format: str = Field(default="pdf", description="Output format (currently only 'pdf' supported)")


class TemplateInfo(BaseModel):
    name: str
    description: str
    preview_url: Optional[str] = None


class HealthResponse(BaseModel):
    status: str
    version: str
    templates_available: int
