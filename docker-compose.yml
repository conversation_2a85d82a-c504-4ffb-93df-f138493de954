version: '3.8'

services:
  resume-api:
    build: .
    container_name: resume-builder-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    # volumes:
      # Optional: Mount local directories for development
      # - ./app:/app/app
      # - ./templates:/app/templates
      # - ./static:/app/static
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/api/v1/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - resume-network

  # Optional: Add nginx reverse proxy for production
  # nginx:
  #   image: nginx:alpine
  #   container_name: resume-nginx
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #   depends_on:
  #     - resume-api
  #   networks:
  #     - resume-network

networks:
  resume-network:
    driver: bridge

# Optional: Add volumes for persistent data
# volumes:
#   resume_data:
